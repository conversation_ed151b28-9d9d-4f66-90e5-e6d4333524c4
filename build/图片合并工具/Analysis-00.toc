(['/Users/<USER>/WK/gitee/tool_img2img/main.py'],
 ['/Users/<USER>/WK/gitee/tool_img2img'],
 [],
 [('/usr/local/lib/python3.11/site-packages/numpy/_pyinstaller', 0),
  ('/usr/local/lib/python3.11/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/usr/local/lib/python3.11/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config.json', '/Users/<USER>/WK/gitee/tool_img2img/config.json', 'DATA')],
 '3.11.3 (main, Apr  7 2023, 19:25:52) [Clang 14.0.0 (clang-1400.0.29.202)]',
 [('pyi_rth_inspect',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', '/Users/<USER>/WK/gitee/tool_img2img/main.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('importlib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tempfile.py',
   'PYMODULE'),
  ('random',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/random.py',
   'PYMODULE'),
  ('_strptime',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_strptime.py',
   'PYMODULE'),
  ('calendar',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/calendar.py',
   'PYMODULE'),
  ('argparse',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/argparse.py',
   'PYMODULE'),
  ('copy',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/copy.py',
   'PYMODULE'),
  ('gettext',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gettext.py',
   'PYMODULE'),
  ('struct',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/struct.py',
   'PYMODULE'),
  ('statistics',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dataclasses.py',
   'PYMODULE'),
  ('inspect',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/inspect.py',
   'PYMODULE'),
  ('token',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/token.py',
   'PYMODULE'),
  ('dis',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/dis.py',
   'PYMODULE'),
  ('opcode',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/opcode.py',
   'PYMODULE'),
  ('ast',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compat_pickle.py',
   'PYMODULE'),
  ('threading',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_threading_local.py',
   'PYMODULE'),
  ('string',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/string.py',
   'PYMODULE'),
  ('bisect',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bisect.py',
   'PYMODULE'),
  ('importlib._abc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_abc.py',
   'PYMODULE'),
  ('contextlib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/__init__.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/base64.py',
   'PYMODULE'),
  ('getopt',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/parse.py',
   'PYMODULE'),
  ('socket',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socket.py',
   'PYMODULE'),
  ('selectors',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/selectors.py',
   'PYMODULE'),
  ('quopri',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/quopri.py',
   'PYMODULE'),
  ('textwrap',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/textwrap.py',
   'PYMODULE'),
  ('zipfile',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipfile.py',
   'PYMODULE'),
  ('py_compile',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/py_compile.py',
   'PYMODULE'),
  ('lzma',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lzma.py',
   'PYMODULE'),
  ('_compression',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_compression.py',
   'PYMODULE'),
  ('bz2',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bz2.py',
   'PYMODULE'),
  ('importlib.util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/util.py',
   'PYMODULE'),
  ('pathlib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pathlib.py',
   'PYMODULE'),
  ('fnmatch',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/fnmatch.py',
   'PYMODULE'),
  ('email',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/email/feedparser.py',
   'PYMODULE'),
  ('csv',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/csv.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/importlib/_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   '/usr/local/lib/python3.11/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   '/usr/local/lib/python3.11/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('setuptools',
   '/usr/local/lib/python3.11/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/text_file.py',
   'PYMODULE'),
  ('distutils',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/__init__.py',
   'PYMODULE'),
  ('_sysconfigdata__darwin_darwin',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_sysconfigdata__darwin_darwin.py',
   'PYMODULE'),
  ('distutils.archive_util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/archive_util.py',
   'PYMODULE'),
  ('tarfile',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/gzip.py',
   'PYMODULE'),
  ('distutils.dir_util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/debug.py',
   'PYMODULE'),
  ('distutils.log',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/log.py',
   'PYMODULE'),
  ('sysconfig',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_bootsubprocess.py',
   'PYMODULE'),
  ('subprocess',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/signal.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/unixccompiler.py',
   'PYMODULE'),
  ('shlex',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shlex.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/py39compat.py',
   'PYMODULE'),
  ('platform',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/platform.py',
   'PYMODULE'),
  ('plistlib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/plistlib.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/expat.py',
   'PYMODULE'),
  ('xml.parsers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/parsers/__init__.py',
   'PYMODULE'),
  ('xml',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/request.py',
   'PYMODULE'),
  ('getpass',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/getpass.py',
   'PYMODULE'),
  ('nturl2path',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ftplib.py',
   'PYMODULE'),
  ('netrc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/netrc.py',
   'PYMODULE'),
  ('mimetypes',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/cookiejar.py',
   'PYMODULE'),
  ('http',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/__init__.py',
   'PYMODULE'),
  ('ssl',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ssl.py',
   'PYMODULE'),
  ('urllib.response',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/response.py',
   'PYMODULE'),
  ('urllib.error',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/urllib/error.py',
   'PYMODULE'),
  ('http.client',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/client.py',
   'PYMODULE'),
  ('xml.sax',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xml/sax/xmlreader.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/version.py',
   'PYMODULE'),
  ('distutils.command',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/command/__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/dist.py',
   'PYMODULE'),
  ('configparser',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/config.py',
   'PYMODULE'),
  ('cgi',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/cgi.py',
   'PYMODULE'),
  ('html',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/html/__init__.py',
   'PYMODULE'),
  ('html.entities',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/html/entities.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/upload.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('glob',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/glob.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/install.py',
   'PYMODULE'),
  ('site',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/site.py',
   'PYMODULE'),
  ('sitecustomize',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/sitecustomize.py',
   'PYMODULE'),
  ('rlcompleter',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/webbrowser.py',
   'PYMODULE'),
  ('http.server',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/http/server.py',
   'PYMODULE'),
  ('socketserver',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pydoc_data/topics.py',
   'PYMODULE'),
  ('pydoc_data',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pydoc_data/__init__.py',
   'PYMODULE'),
  ('tty',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tty.py',
   'PYMODULE'),
  ('pkgutil',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/zipimport.py',
   'PYMODULE'),
  ('__future__',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/__future__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/thread.py',
   'PYMODULE'),
  ('queue',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/queue.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/runpy.py',
   'PYMODULE'),
  ('multiprocessing.util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/secrets.py',
   'PYMODULE'),
  ('hmac',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/client.py',
   'PYMODULE'),
  ('xmlrpc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/xmlrpc/__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/multiprocessing/__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/concurrent/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/mock.py',
   'PYMODULE'),
  ('unittest',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/signals.py',
   'PYMODULE'),
  ('unittest.main',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/main.py',
   'PYMODULE'),
  ('unittest.runner',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/runner.py',
   'PYMODULE'),
  ('unittest.loader',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/loader.py',
   'PYMODULE'),
  ('unittest.suite',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/suite.py',
   'PYMODULE'),
  ('unittest.case',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/case.py',
   'PYMODULE'),
  ('unittest._log',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/_log.py',
   'PYMODULE'),
  ('difflib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/difflib.py',
   'PYMODULE'),
  ('unittest.result',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/result.py',
   'PYMODULE'),
  ('unittest.util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/unittest/util.py',
   'PYMODULE'),
  ('asyncio',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/asyncio/constants.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   '/usr/local/lib/python3.11/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   '/usr/local/lib/python3.11/site-packages/setuptools/extern/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/jaraco/functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   '/usr/local/lib/python3.11/site-packages/setuptools/_vendor/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/usr/local/lib/python3.11/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/command/build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/extension.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/usr/local/lib/python3.11/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/usr/local/lib/python3.11/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('distutils.filelist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/usr/local/lib/python3.11/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/usr/local/lib/python3.11/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/usr/local/lib/python3.11/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/usr/local/lib/python3.11/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/py34compat.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/usr/local/lib/python3.11/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/usr/local/lib/python3.11/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('pkg_resources',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.version',
   '/usr/local/lib/python3.11/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging.utils',
   '/usr/local/lib/python3.11/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging.tags',
   '/usr/local/lib/python3.11/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/usr/local/lib/python3.11/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/usr/local/lib/python3.11/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/usr/local/lib/python3.11/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   '/usr/local/lib/python3.11/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/usr/local/lib/python3.11/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/usr/local/lib/python3.11/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/usr/local/lib/python3.11/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   '/usr/local/lib/python3.11/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   '/usr/local/lib/python3.11/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/usr/local/lib/python3.11/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/usr/local/lib/python3.11/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/usr/local/lib/python3.11/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging',
   '/usr/local/lib/python3.11/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/__main__.py',
   'PYMODULE'),
  ('platformdirs',
   '/usr/local/lib/python3.11/site-packages/platformdirs/__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   '/usr/local/lib/python3.11/site-packages/platformdirs/android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   '/usr/local/lib/python3.11/site-packages/platformdirs/unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   '/usr/local/lib/python3.11/site-packages/platformdirs/macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   '/usr/local/lib/python3.11/site-packages/platformdirs/windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   '/usr/local/lib/python3.11/site-packages/platformdirs/version.py',
   'PYMODULE'),
  ('platformdirs.api',
   '/usr/local/lib/python3.11/site-packages/platformdirs/api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/jaraco/functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/jaraco/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/importlib_resources/__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/_vendor/__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   '/usr/local/lib/python3.11/site-packages/pkg_resources/extern/__init__.py',
   'PYMODULE'),
  ('imp',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/imp.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/usr/local/lib/python3.11/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/usr/local/lib/python3.11/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   '/usr/local/lib/python3.11/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/usr/local/lib/python3.11/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/usr/local/lib/python3.11/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('typing_extensions',
   '/usr/local/lib/python3.11/site-packages/typing_extensions.py',
   'PYMODULE'),
  ('distutils.command.build',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/py36compat.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('trove_classifiers',
   '/usr/local/lib/python3.11/site-packages/trove_classifiers/__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   '/usr/local/lib/python3.11/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools.command',
   '/usr/local/lib/python3.11/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/usr/local/lib/python3.11/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   '/usr/local/lib/python3.11/site-packages/zipp/compat/overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   '/usr/local/lib/python3.11/site-packages/zipp/compat/__init__.py',
   'PYMODULE'),
  ('zipp',
   '/usr/local/lib/python3.11/site-packages/zipp/__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   '/usr/local/lib/python3.11/site-packages/zipp/_functools.py',
   'PYMODULE'),
  ('zipp.glob',
   '/usr/local/lib/python3.11/site-packages/zipp/glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   '/usr/local/lib/python3.11/site-packages/zipp/compat/py310.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/usr/local/lib/python3.11/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/usr/local/lib/python3.11/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/usr/local/lib/python3.11/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/usr/local/lib/python3.11/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/usr/local/lib/python3.11/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/usr/local/lib/python3.11/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools.version',
   '/usr/local/lib/python3.11/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   '/usr/local/lib/python3.11/site-packages/setuptools/_deprecation_warning.py',
   'PYMODULE'),
  ('distutils.util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/util.py',
   'PYMODULE'),
  ('distutils.errors',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/errors.py',
   'PYMODULE'),
  ('distutils.core',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/core.py',
   'PYMODULE'),
  ('distutils.config',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/distutils/config.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/usr/local/lib/python3.11/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('_py_abc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/tracemalloc.py',
   'PYMODULE'),
  ('stringprep',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/stringprep.py',
   'PYMODULE'),
  ('shutil',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/shutil.py',
   'PYMODULE'),
  ('PIL.Image',
   '/usr/local/lib/python3.11/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/usr/local/lib/python3.11/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/usr/local/lib/python3.11/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/usr/local/lib/python3.11/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/colorsys.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   '/usr/local/lib/python3.11/site-packages/PIL/PyAccess.py',
   'PYMODULE'),
  ('numpy',
   '/usr/local/lib/python3.11/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   '/usr/local/lib/python3.11/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy._version',
   '/usr/local/lib/python3.11/site-packages/numpy/_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/usr/local/lib/python3.11/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   '/usr/local/lib/python3.11/site-packages/numpy/distutils/cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   '/usr/local/lib/python3.11/site-packages/numpy/distutils/__init__.py',
   'PYMODULE'),
  ('doctest',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/doctest.py',
   'PYMODULE'),
  ('pdb',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/pdb.py',
   'PYMODULE'),
  ('code',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/code.py',
   'PYMODULE'),
  ('codeop',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/codeop.py',
   'PYMODULE'),
  ('bdb',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/bdb.py',
   'PYMODULE'),
  ('cmd',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/cmd.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   '/usr/local/lib/python3.11/site-packages/numpy/core/fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   '/usr/local/lib/python3.11/site-packages/numpy/core/numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   '/usr/local/lib/python3.11/site-packages/numpy/core/arrayprint.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   '/usr/local/lib/python3.11/site-packages/numpy/core/shape_base.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   '/usr/local/lib/python3.11/site-packages/numpy/core/umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   '/usr/local/lib/python3.11/site-packages/numpy/core/overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   '/usr/local/lib/python3.11/site-packages/numpy/compat/_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   '/usr/local/lib/python3.11/site-packages/numpy/core/multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   '/usr/local/lib/python3.11/site-packages/numpy/core/numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_string_helpers.py',
   'PYMODULE'),
  ('numpy.testing._private',
   '/usr/local/lib/python3.11/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/usr/local/lib/python3.11/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/usr/local/lib/python3.11/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   '/usr/local/lib/python3.11/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/usr/local/lib/python3.11/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   '/usr/local/lib/python3.11/site-packages/numpy/core/function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/usr/local/lib/python3.11/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/usr/local/lib/python3.11/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_internal.py',
   'PYMODULE'),
  ('numpy.random',
   '/usr/local/lib/python3.11/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/usr/local/lib/python3.11/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   '/usr/local/lib/python3.11/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/usr/local/lib/python3.11/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/usr/local/lib/python3.11/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/usr/local/lib/python3.11/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   '/usr/local/lib/python3.11/site-packages/numpy/core/records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   '/usr/local/lib/python3.11/site-packages/numpy/core/getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/usr/local/lib/python3.11/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   '/usr/local/lib/python3.11/site-packages/numpy/compat/__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   '/usr/local/lib/python3.11/site-packages/numpy/compat/py3k.py',
   'PYMODULE'),
  ('numpy.core',
   '/usr/local/lib/python3.11/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   '/usr/local/lib/python3.11/site-packages/numpy/core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   '/usr/local/lib/python3.11/site-packages/numpy/core/memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   '/usr/local/lib/python3.11/site-packages/numpy/core/defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/usr/local/lib/python3.11/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/usr/local/lib/python3.11/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   '/usr/local/lib/python3.11/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/usr/local/lib/python3.11/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   '/usr/local/lib/python3.11/site-packages/numpy/array_api/_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   '/usr/local/lib/python3.11/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/usr/local/lib/python3.11/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/usr/local/lib/python3.11/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/usr/local/lib/python3.11/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/usr/local/lib/python3.11/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/usr/local/lib/python3.11/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/usr/local/lib/python3.11/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('cffi',
   '/usr/local/lib/python3.11/site-packages/cffi/__init__.py',
   'PYMODULE'),
  ('cffi.error',
   '/usr/local/lib/python3.11/site-packages/cffi/error.py',
   'PYMODULE'),
  ('cffi.api',
   '/usr/local/lib/python3.11/site-packages/cffi/api.py',
   'PYMODULE'),
  ('ctypes.util',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/util.py',
   'PYMODULE'),
  ('ctypes._aix',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/macholib/dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/macholib/__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/macholib/dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/ctypes/macholib/framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   '/usr/local/lib/python3.11/site-packages/cffi/recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   '/usr/local/lib/python3.11/site-packages/cffi/cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   '/usr/local/lib/python3.11/site-packages/cffi/_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   '/usr/local/lib/python3.11/site-packages/cffi/verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   '/usr/local/lib/python3.11/site-packages/cffi/lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   '/usr/local/lib/python3.11/site-packages/cffi/pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   '/usr/local/lib/python3.11/site-packages/cffi/vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   '/usr/local/lib/python3.11/site-packages/cffi/_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   '/usr/local/lib/python3.11/site-packages/cffi/vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   '/usr/local/lib/python3.11/site-packages/cffi/ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   '/usr/local/lib/python3.11/site-packages/cffi/cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   '/usr/local/lib/python3.11/site-packages/pycparser/lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   '/usr/local/lib/python3.11/site-packages/pycparser/yacctab.py',
   'PYMODULE'),
  ('pycparser',
   '/usr/local/lib/python3.11/site-packages/pycparser/__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   '/usr/local/lib/python3.11/site-packages/pycparser/c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   '/usr/local/lib/python3.11/site-packages/pycparser/ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   '/usr/local/lib/python3.11/site-packages/pycparser/plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   '/usr/local/lib/python3.11/site-packages/pycparser/c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   '/usr/local/lib/python3.11/site-packages/pycparser/ply/lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   '/usr/local/lib/python3.11/site-packages/pycparser/ply/yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   '/usr/local/lib/python3.11/site-packages/pycparser/ply/__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   '/usr/local/lib/python3.11/site-packages/pycparser/c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   '/usr/local/lib/python3.11/site-packages/cffi/commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   '/usr/local/lib/python3.11/site-packages/cffi/model.py',
   'PYMODULE'),
  ('PIL._util',
   '/usr/local/lib/python3.11/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._binary',
   '/usr/local/lib/python3.11/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/usr/local/lib/python3.11/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/usr/local/lib/python3.11/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/usr/local/lib/python3.11/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL',
   '/usr/local/lib/python3.11/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL._version',
   '/usr/local/lib/python3.11/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('PySide6',
   '/usr/local/lib/python3.11/site-packages/PySide6/__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   '/usr/local/lib/python3.11/site-packages/PySide6/support/deprecated.py',
   'PYMODULE'),
  ('PySide6.support',
   '/usr/local/lib/python3.11/site-packages/PySide6/support/__init__.py',
   'PYMODULE'),
  ('shiboken6',
   '/usr/local/lib/python3.11/site-packages/shiboken6/__init__.py',
   'PYMODULE'),
  ('datetime',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/datetime.py',
   'PYMODULE'),
  ('json',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/json/scanner.py',
   'PYMODULE')],
 [('Python.framework/Versions/3.11/Python',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/Python',
   'BINARY'),
  ('numpy/.dylibs/libquadmath.0.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libquadmath.0.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgcc_s.1.1.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libgcc_s.1.1.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgfortran.5.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libgfortran.5.dylib',
   'BINARY'),
  ('numpy/.dylibs/libopenblas64_.0.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libopenblas64_.0.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqsecuretransportbackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqsecuretransportbackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqopensslbackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqopensslbackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqcertonlybackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqcertonlybackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/networkinformation/libqscnetworkreachability.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/networkinformation/libqscnetworkreachability.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/styles/libqmacstyle.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqsvg.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqpdf.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqcocoa.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforminputcontexts/libqtvirtualkeyboardplugin.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforminputcontexts/libqtvirtualkeyboardplugin.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqwebp.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqjpeg.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqoffscreen.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqwbmp.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqtga.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqminimal.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/iconengines/libqsvgicon.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqico.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqgif.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqmacjp2.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/generic/libqtuiotouchplugin.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqicns.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqtiff.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqmacheif.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('lib-dynload/_typing.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_typing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_statistics.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_contextvars.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_decimal.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_opcode.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_pickle.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_hashlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_blake2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha256.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_md5.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha1.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha512.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_random.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bisect.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/math.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/unicodedata.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/array.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/select.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_socket.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/binascii.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_lzma.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bz2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/grp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_csv.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/resource.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/fcntl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_scproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/termios.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ssl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/pyexpat.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/readline.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_queue.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/mmap.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixshmem.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ctypes.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multiprocessing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_asyncio.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/syslog.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_heapq.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multibytecodec.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_jp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_kr.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_cn.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_tw.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_hk.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_webp.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imagingtk.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imagingcms.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_tests.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_multiarray_tests.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_umath.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_multiarray_umath.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/lapack_lite.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/lapack_lite.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/mtrand.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_sfc64.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_philox.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_pcg64.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_mt19937.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/bit_generator.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_generator.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_bounded_integers.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_common.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_internal.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/fft/_pocketfft_internal.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/_umath_linalg.cpython-311-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/_cffi_backend.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imaging.cpython-311-darwin.so',
   'EXTENSION'),
  ('PySide6/QtCore.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtCore.abi3.so',
   'EXTENSION'),
  ('PySide6/QtNetwork.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtNetwork.abi3.so',
   'EXTENSION'),
  ('shiboken6/Shiboken.abi3.so',
   '/usr/local/lib/python3.11/site-packages/shiboken6/Shiboken.abi3.so',
   'EXTENSION'),
  ('PySide6/QtWidgets.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PySide6/QtGui.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtGui.abi3.so',
   'EXTENSION'),
  ('PySide6/QtDBus.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtDBus.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_datetime.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_json.cpython-311-darwin.so',
   'EXTENSION'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   'BINARY'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml',
   'BINARY'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'BINARY'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   'BINARY'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick',
   'BINARY'),
  ('libmpdec.3.dylib',
   '/usr/local/opt/mpdecimal/lib/libmpdec.3.dylib',
   'BINARY'),
  ('libcrypto.1.1.dylib',
   '/usr/local/opt/openssl@1.1/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('libssl.1.1.dylib',
   '/usr/local/opt/openssl@1.1/lib/libssl.1.1.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.2.13.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libz.1.2.13.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libopenjp2.2.5.0.dylib',
   'BINARY'),
  ('PySide6/libpyside6.abi3.6.5.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/libpyside6.abi3.6.5.dylib',
   'BINARY'),
  ('shiboken6/libshiboken6.abi3.6.5.dylib',
   '/usr/local/lib/python3.11/site-packages/shiboken6/libshiboken6.abi3.6.5.dylib',
   'BINARY'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.0.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libXau.6.0.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY')],
 [],
 [],
 [('config.json', '/Users/<USER>/WK/gitee/tool_img2img/config.json', 'DATA'),
  ('importlib_metadata-8.6.1.dist-info/WHEEL',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/WHEEL',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/top_level.txt',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/LICENSE',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/METADATA',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/RECORD',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/INSTALLER',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/INSTALLER',
   'DATA'),
  ('PySide6/Qt/translations/qt_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_lv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_lv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fa.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fa.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_pl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_he.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_he.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_sl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_sl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pt_PT.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pt_PT.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_pl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_gd.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_gd.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_he.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_he.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_gd.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_gd.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fi.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fi.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_gl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_gl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_lt.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_lt.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_lv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_lv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fa.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fa.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fi.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fi.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_gl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_gl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pl.qm',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.11/Python', 'SYMLINK'),
  ('wheel-0.40.0.dist-info/LICENSE.txt',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/LICENSE.txt',
   'DATA'),
  ('wheel-0.40.0.dist-info/WHEEL',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/WHEEL',
   'DATA'),
  ('wheel-0.40.0.dist-info/direct_url.json',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/direct_url.json',
   'DATA'),
  ('wheel-0.40.0.dist-info/METADATA',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/METADATA',
   'DATA'),
  ('wheel-0.40.0.dist-info/entry_points.txt',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.40.0.dist-info/INSTALLER',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.40.0.dist-info/REQUESTED',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/REQUESTED',
   'DATA'),
  ('wheel-0.40.0.dist-info/RECORD',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/RECORD',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/WK/gitee/tool_img2img/build/图片合并工具/base_library.zip',
   'DATA'),
  ('libquadmath.0.dylib', 'numpy/.dylibs/libquadmath.0.dylib', 'SYMLINK'),
  ('libgcc_s.1.1.dylib', 'numpy/.dylibs/libgcc_s.1.1.dylib', 'SYMLINK'),
  ('libgfortran.5.dylib', 'numpy/.dylibs/libgfortran.5.dylib', 'SYMLINK'),
  ('QtNetwork',
   'PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtCore', 'PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtWidgets',
   'PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtGui', 'PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtSvg', 'PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtPdf', 'PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtVirtualKeyboard',
   'PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   'SYMLINK'),
  ('QtQml', 'PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml', 'SYMLINK'),
  ('QtOpenGL',
   'PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'SYMLINK'),
  ('QtQmlModels',
   'PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   'SYMLINK'),
  ('QtQuick', 'PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libopenblas64_.0.dylib', 'numpy/.dylibs/libopenblas64_.0.dylib', 'SYMLINK'),
  ('libz.1.2.13.dylib', 'PIL/.dylibs/libz.1.2.13.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.0.dylib', 'PIL/.dylibs/libopenjp2.2.5.0.dylib', 'SYMLINK'),
  ('libpyside6.abi3.6.5.dylib', 'PySide6/libpyside6.abi3.6.5.dylib', 'SYMLINK'),
  ('libshiboken6.abi3.6.5.dylib',
   'shiboken6/libshiboken6.abi3.6.5.dylib',
   'SYMLINK'),
  ('QtDBus', 'PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.0.0.dylib', 'PIL/.dylibs/libXau.6.0.0.dylib', 'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQml.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQmlModels.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQuick.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/QtVirtualKeyboard',
   'Versions/Current/QtVirtualKeyboard',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/Current',
   'A',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.11/Resources/Info.plist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.11', 'SYMLINK')])
