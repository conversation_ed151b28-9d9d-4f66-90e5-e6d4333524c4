([('图片合并工具',
   '/Users/<USER>/WK/gitee/tool_img2img/build/图片合并工具/图片合并工具',
   'EXECUTABLE'),
  ('lib-dynload/_struct.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_struct.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/zlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('Python.framework/Versions/3.11/Python',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/Python',
   'BINARY'),
  ('numpy/.dylibs/libquadmath.0.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libquadmath.0.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgcc_s.1.1.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libgcc_s.1.1.dylib',
   'BINARY'),
  ('numpy/.dylibs/libgfortran.5.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libgfortran.5.dylib',
   'BINARY'),
  ('numpy/.dylibs/libopenblas64_.0.dylib',
   '/usr/local/lib/python3.11/site-packages/numpy/.dylibs/libopenblas64_.0.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqopensslbackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqopensslbackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqsecuretransportbackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqsecuretransportbackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/tls/libqcertonlybackend.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/tls/libqcertonlybackend.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/networkinformation/libqscnetworkreachability.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/networkinformation/libqscnetworkreachability.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/styles/libqmacstyle.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/styles/libqmacstyle.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqoffscreen.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqoffscreen.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqico.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqico.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/iconengines/libqsvgicon.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/iconengines/libqsvgicon.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqtga.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqtga.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqsvg.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqsvg.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqicns.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqicns.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqmacheif.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqmacheif.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqwebp.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqwebp.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqpdf.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqpdf.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqgif.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqgif.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/generic/libqtuiotouchplugin.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/generic/libqtuiotouchplugin.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqmacjp2.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqmacjp2.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqcocoa.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqcocoa.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqwbmp.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqwbmp.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqtiff.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqtiff.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/imageformats/libqjpeg.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/imageformats/libqjpeg.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforminputcontexts/libqtvirtualkeyboardplugin.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforminputcontexts/libqtvirtualkeyboardplugin.dylib',
   'BINARY'),
  ('PySide6/Qt/plugins/platforms/libqminimal.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/plugins/platforms/libqminimal.dylib',
   'BINARY'),
  ('lib-dynload/_typing.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_typing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_statistics.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_contextvars.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_decimal.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_opcode.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_pickle.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_hashlib.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha3.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_blake2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha256.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha256.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_md5.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha1.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha512.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_sha512.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_random.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bisect.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/math.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/unicodedata.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/array.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/select.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_socket.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/binascii.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_lzma.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_bz2.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/grp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_csv.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/resource.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixsubprocess.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/fcntl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_scproxy.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_scproxy.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/termios.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ssl.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/pyexpat.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/pyexpat.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/readline.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_queue.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/mmap.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_posixshmem.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_ctypes.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multiprocessing.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_asyncio.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/syslog.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/syslog.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_multibytecodec.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_jp.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_kr.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_iso2022.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_cn.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_tw.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_codecs_hk.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_heapq.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_webp.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imagingtk.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imagingcms.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_tests.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_multiarray_tests.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_umath.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/core/_multiarray_umath.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/lapack_lite.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/lapack_lite.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/mtrand.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_sfc64.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_philox.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_pcg64.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_mt19937.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/bit_generator.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_generator.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_bounded_integers.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/random/_common.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_internal.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/fft/_pocketfft_internal.cpython-311-darwin.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/numpy/linalg/_umath_linalg.cpython-311-darwin.so',
   'EXTENSION'),
  ('_cffi_backend.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/_cffi_backend.cpython-311-darwin.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-311-darwin.so',
   '/usr/local/lib/python3.11/site-packages/PIL/_imaging.cpython-311-darwin.so',
   'EXTENSION'),
  ('PySide6/QtCore.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtCore.abi3.so',
   'EXTENSION'),
  ('PySide6/QtNetwork.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtNetwork.abi3.so',
   'EXTENSION'),
  ('shiboken6/Shiboken.abi3.so',
   '/usr/local/lib/python3.11/site-packages/shiboken6/Shiboken.abi3.so',
   'EXTENSION'),
  ('PySide6/QtWidgets.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtWidgets.abi3.so',
   'EXTENSION'),
  ('PySide6/QtGui.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtGui.abi3.so',
   'EXTENSION'),
  ('PySide6/QtDBus.abi3.so',
   '/usr/local/lib/python3.11/site-packages/PySide6/QtDBus.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_datetime.cpython-311-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-311-darwin.so',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/lib/python3.11/lib-dynload/_json.cpython-311-darwin.so',
   'EXTENSION'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'BINARY'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore',
   'BINARY'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'BINARY'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui',
   'BINARY'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg',
   'BINARY'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf',
   'BINARY'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   'BINARY'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   'BINARY'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'BINARY'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml',
   'BINARY'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick',
   'BINARY'),
  ('libmpdec.3.dylib',
   '/usr/local/opt/mpdecimal/lib/libmpdec.3.dylib',
   'BINARY'),
  ('libcrypto.1.1.dylib',
   '/usr/local/opt/openssl@1.1/lib/libcrypto.1.1.dylib',
   'BINARY'),
  ('libssl.1.1.dylib',
   '/usr/local/opt/openssl@1.1/lib/libssl.1.1.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpdemux.2.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebpdemux.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebpmux.3.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebpmux.3.dylib',
   'BINARY'),
  ('PIL/.dylibs/libwebp.7.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libwebp.7.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblcms2.2.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/liblcms2.2.dylib',
   'BINARY'),
  ('PIL/.dylibs/libtiff.6.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libtiff.6.dylib',
   'BINARY'),
  ('PIL/.dylibs/libz.1.2.13.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libz.1.2.13.dylib',
   'BINARY'),
  ('PIL/.dylibs/libopenjp2.2.5.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libopenjp2.2.5.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/libxcb.1.1.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libxcb.1.1.0.dylib',
   'BINARY'),
  ('PySide6/libpyside6.abi3.6.5.dylib',
   '/usr/local/lib/python3.11/site-packages/PySide6/libpyside6.abi3.6.5.dylib',
   'BINARY'),
  ('shiboken6/libshiboken6.abi3.6.5.dylib',
   '/usr/local/lib/python3.11/site-packages/shiboken6/libshiboken6.abi3.6.5.dylib',
   'BINARY'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus',
   'BINARY'),
  ('PIL/.dylibs/libsharpyuv.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libsharpyuv.0.dylib',
   'BINARY'),
  ('PIL/.dylibs/liblzma.5.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/liblzma.5.dylib',
   'BINARY'),
  ('PIL/.dylibs/libXau.6.0.0.dylib',
   '/usr/local/lib/python3.11/site-packages/PIL/.dylibs/libXau.6.0.0.dylib',
   'BINARY'),
  ('config.json', '/Users/<USER>/WK/gitee/tool_img2img/config.json', 'DATA'),
  ('importlib_metadata-8.6.1.dist-info/top_level.txt',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/top_level.txt',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/METADATA',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/METADATA',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/INSTALLER',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/INSTALLER',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/LICENSE',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/LICENSE',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/RECORD',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/RECORD',
   'DATA'),
  ('importlib_metadata-8.6.1.dist-info/WHEEL',
   '/usr/local/lib/python3.11/site-packages/importlib_metadata-8.6.1.dist-info/WHEEL',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fi.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fi.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fi.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fi.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fa.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fa.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pt_PT.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pt_PT.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_uk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_uk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_gd.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_gd.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_fa.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_fa.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_he.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_he.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_gl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_gl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ko.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ko.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_sk.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_sk.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_pl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_gl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_gl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_lv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_lv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_hu.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_hu.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_he.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_he.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_da.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_da.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ar.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ar.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_lv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_lv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_tr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_tr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_nl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_nl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_hr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_hr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_zh_TW.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_de.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_de.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_lt.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_lt.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sv.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sv.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_sl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_sl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_pt_BR.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_bg.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_bg.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_cs.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_cs.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_it.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_it.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ru.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ru.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_fr.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_fr.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_zh_CN.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_en.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_en.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_ja.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_ja.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_ca.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_ca.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_es.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_es.qm',
   'DATA'),
  ('PySide6/Qt/translations/qtbase_pl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qtbase_pl.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_gd.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_gd.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_nn.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_nn.qm',
   'DATA'),
  ('PySide6/Qt/translations/qt_help_sl.qm',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/translations/qt_help_sl.qm',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.11/Python', 'SYMLINK'),
  ('wheel-0.40.0.dist-info/WHEEL',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/WHEEL',
   'DATA'),
  ('wheel-0.40.0.dist-info/direct_url.json',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/direct_url.json',
   'DATA'),
  ('wheel-0.40.0.dist-info/INSTALLER',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/INSTALLER',
   'DATA'),
  ('wheel-0.40.0.dist-info/METADATA',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/METADATA',
   'DATA'),
  ('wheel-0.40.0.dist-info/REQUESTED',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/REQUESTED',
   'DATA'),
  ('wheel-0.40.0.dist-info/RECORD',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/RECORD',
   'DATA'),
  ('wheel-0.40.0.dist-info/entry_points.txt',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/entry_points.txt',
   'DATA'),
  ('wheel-0.40.0.dist-info/LICENSE.txt',
   '/usr/local/lib/python3.11/site-packages/wheel-0.40.0.dist-info/LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   '/Users/<USER>/WK/gitee/tool_img2img/build/图片合并工具/base_library.zip',
   'DATA'),
  ('libgcc_s.1.1.dylib', 'numpy/.dylibs/libgcc_s.1.1.dylib', 'SYMLINK'),
  ('libquadmath.0.dylib', 'numpy/.dylibs/libquadmath.0.dylib', 'SYMLINK'),
  ('libgfortran.5.dylib', 'numpy/.dylibs/libgfortran.5.dylib', 'SYMLINK'),
  ('QtNetwork',
   'PySide6/Qt/lib/QtNetwork.framework/Versions/A/QtNetwork',
   'SYMLINK'),
  ('QtCore', 'PySide6/Qt/lib/QtCore.framework/Versions/A/QtCore', 'SYMLINK'),
  ('QtWidgets',
   'PySide6/Qt/lib/QtWidgets.framework/Versions/A/QtWidgets',
   'SYMLINK'),
  ('QtGui', 'PySide6/Qt/lib/QtGui.framework/Versions/A/QtGui', 'SYMLINK'),
  ('QtSvg', 'PySide6/Qt/lib/QtSvg.framework/Versions/A/QtSvg', 'SYMLINK'),
  ('QtPdf', 'PySide6/Qt/lib/QtPdf.framework/Versions/A/QtPdf', 'SYMLINK'),
  ('QtQmlModels',
   'PySide6/Qt/lib/QtQmlModels.framework/Versions/A/QtQmlModels',
   'SYMLINK'),
  ('QtVirtualKeyboard',
   'PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/QtVirtualKeyboard',
   'SYMLINK'),
  ('QtOpenGL',
   'PySide6/Qt/lib/QtOpenGL.framework/Versions/A/QtOpenGL',
   'SYMLINK'),
  ('QtQml', 'PySide6/Qt/lib/QtQml.framework/Versions/A/QtQml', 'SYMLINK'),
  ('QtQuick', 'PySide6/Qt/lib/QtQuick.framework/Versions/A/QtQuick', 'SYMLINK'),
  ('liblzma.5.dylib', 'PIL/.dylibs/liblzma.5.dylib', 'SYMLINK'),
  ('libwebpdemux.2.dylib', 'PIL/.dylibs/libwebpdemux.2.dylib', 'SYMLINK'),
  ('libwebpmux.3.dylib', 'PIL/.dylibs/libwebpmux.3.dylib', 'SYMLINK'),
  ('libwebp.7.dylib', 'PIL/.dylibs/libwebp.7.dylib', 'SYMLINK'),
  ('liblcms2.2.dylib', 'PIL/.dylibs/liblcms2.2.dylib', 'SYMLINK'),
  ('libopenblas64_.0.dylib', 'numpy/.dylibs/libopenblas64_.0.dylib', 'SYMLINK'),
  ('libtiff.6.dylib', 'PIL/.dylibs/libtiff.6.dylib', 'SYMLINK'),
  ('libz.1.2.13.dylib', 'PIL/.dylibs/libz.1.2.13.dylib', 'SYMLINK'),
  ('libopenjp2.2.5.0.dylib', 'PIL/.dylibs/libopenjp2.2.5.0.dylib', 'SYMLINK'),
  ('libxcb.1.1.0.dylib', 'PIL/.dylibs/libxcb.1.1.0.dylib', 'SYMLINK'),
  ('libpyside6.abi3.6.5.dylib', 'PySide6/libpyside6.abi3.6.5.dylib', 'SYMLINK'),
  ('libshiboken6.abi3.6.5.dylib',
   'shiboken6/libshiboken6.abi3.6.5.dylib',
   'SYMLINK'),
  ('QtDBus', 'PySide6/Qt/lib/QtDBus.framework/Versions/A/QtDBus', 'SYMLINK'),
  ('libsharpyuv.0.dylib', 'PIL/.dylibs/libsharpyuv.0.dylib', 'SYMLINK'),
  ('libXau.6.0.0.dylib', 'PIL/.dylibs/libXau.6.0.0.dylib', 'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/QtCore',
   'Versions/Current/QtCore',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtCore.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtCore.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/QtDBus',
   'Versions/Current/QtDBus',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtDBus.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtDBus.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/QtGui', 'Versions/Current/QtGui', 'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtGui.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtGui.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/QtNetwork',
   'Versions/Current/QtNetwork',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtNetwork.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtNetwork.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/QtOpenGL',
   'Versions/Current/QtOpenGL',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtOpenGL.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtOpenGL.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/QtPdf', 'Versions/Current/QtPdf', 'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtPdf.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtPdf.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/QtQml', 'Versions/Current/QtQml', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQml.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQml.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/QtQmlModels',
   'Versions/Current/QtQmlModels',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQmlModels.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQmlModels.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/QtQuick',
   'Versions/Current/QtQuick',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtQuick.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtQuick.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/QtSvg', 'Versions/Current/QtSvg', 'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtSvg.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtSvg.framework/Versions/Current', 'A', 'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/QtVirtualKeyboard',
   'Versions/Current/QtVirtualKeyboard',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtVirtualKeyboard.framework/Versions/Current',
   'A',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/QtWidgets',
   'Versions/Current/QtWidgets',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/Resources',
   'Versions/Current/Resources',
   'SYMLINK'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   '/usr/local/lib/python3.11/site-packages/PySide6/Qt/lib/QtWidgets.framework/Versions/A/Resources/Info.plist',
   'DATA'),
  ('PySide6/Qt/lib/QtWidgets.framework/Versions/Current', 'A', 'SYMLINK'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.11/Resources/Info.plist',
   '/usr/local/Cellar/python@3.11/3.11.3/Frameworks/Python.framework/Versions/3.11/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.11', 'SYMLINK')],)
